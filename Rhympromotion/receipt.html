<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>إيصال استلام - شركة إيكوم</title>
    <link
      href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "IBM Plex Sans Arabic", sans-serif;
        background-color: white;
        padding: 20px;
        direction: rtl;
        color: black;
      }

      .receipt-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border: 2px solid black;
        overflow: hidden;
      }

      .header {
        background: white;
        color: black;
        padding: 30px;
        text-align: center;
        border-bottom: 2px solid black;
      }

      .logo {
        max-width: 200px;
        height: auto;
        margin-bottom: 20px;
        background: white;
        padding: 10px;
        border: 1px solid black;
      }

      .company-name {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .receipt-title {
        font-size: 24px;
        margin-top: 20px;
        border: 2px solid black;
        padding: 10px;
        background: black;
        color: white;
      }

      .receipt-body {
        padding: 40px;
      }

      .receipt-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
      }

      .info-item {
        padding: 15px;
        background: white;
        border: 1px solid black;
      }

      .info-label {
        font-weight: bold;
        color: black;
        margin-bottom: 5px;
      }

      .info-value {
        font-size: 16px;
        color: black;
      }

      .service-details {
        background: white;
        padding: 25px;
        margin: 30px 0;
        border: 2px solid black;
      }

      .service-title {
        font-size: 20px;
        font-weight: bold;
        color: black;
        margin-bottom: 15px;
        text-align: center;
      }

      .service-description {
        font-size: 16px;
        line-height: 1.6;
        color: black;
        text-align: center;
      }

      .website-url {
        background: black;
        color: white;
        padding: 10px;
        text-align: center;
        margin-top: 15px;
        font-weight: bold;
      }

      .amount-section {
        background: black;
        color: white;
        padding: 25px;
        text-align: center;
        margin: 30px 0;
      }

      .amount-label {
        font-size: 18px;
        margin-bottom: 10px;
      }

      .amount-value {
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .amount-words {
        font-size: 16px;
        font-style: italic;
      }

      .signature-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        margin-top: 50px;
        padding-top: 30px;
        border-top: 2px solid black;
      }

      .signature-box {
        text-align: center;
        padding: 20px;
      }

      .signature-title {
        font-weight: bold;
        color: black;
        margin-bottom: 60px;
        font-size: 18px;
      }

      .signature-line {
        border-bottom: 2px solid black;
        margin-bottom: 10px;
        height: 80px;
        position: relative;
      }

      .stamp-area {
        width: 150px;
        height: 150px;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
      }

      .stamp-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }

      .signature-name {
        font-family: "Brush Script MT", "Lucida Handwriting", "Segoe Script",
          cursive;
        font-style: italic;
        font-size: 24px;
        color: #2c3e50;
        transform: rotate(-3deg);
        margin-top: 15px;
        font-weight: 300;
        letter-spacing: 2px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        position: relative;
      }

      .signature-name::after {
        content: "";
        position: absolute;
        bottom: -5px;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(
          to right,
          transparent,
          #2c3e50,
          transparent
        );
      }

      .footer {
        background: black;
        color: white;
        padding: 20px;
        text-align: center;
      }

      .date-section {
        text-align: left;
        margin-bottom: 20px;
        font-size: 16px;
        color: black;
      }

      @media print {
        body {
          background: white;
          padding: 0;
        }

        .receipt-container {
          box-shadow: none;
          border-radius: 0;
        }
      }
    </style>
  </head>
  <body>
    <div class="receipt-container">
      <div class="header">
        <img
          src="https://ecom-eg.net/wp-content/uploads/2023/04/E-COM-LOGO-DESIGN-1.png"
          alt="E-COM Logo"
          class="logo"
        />
        <div class="company-name">شركة إيكوم للحلول التقنية</div>
        <div class="receipt-title">إيصال استلام</div>
      </div>

      <div class="receipt-body">
        <div class="date-section">التاريخ: <span id="current-date"></span></div>

        <div class="receipt-info">
          <div class="info-item">
            <div class="info-label">رقم الإيصال:</div>
            <div class="info-value">#REC-2025-001</div>
          </div>
          <div class="info-item">
            <div class="info-label">العميل:</div>
            <div class="info-value">Rhym Promotion</div>
          </div>
        </div>

        <div class="service-details">
          <div class="service-title">تفاصيل الخدمة</div>
          <div class="service-description">
            ثمن خدمة تصميم وبرمجة موقع إلكتروني متكامل
            <div class="website-url">https://rhympromotion.com/</div>
          </div>
        </div>

        <div class="amount-section">
          <div class="amount-label">المبلغ المستلم</div>
          <div class="amount-value">٩٦٠٠ جنيه مصري</div>
          <div class="amount-words">تسعة آلاف وستمائة جنيه مصري لا غير</div>
        </div>

        <div class="signature-section">
          <div class="signature-box">
            <div class="signature-title">ختم الشركة</div>
            <div class="stamp-area">
              <img
                src="../signature.png"
                alt="ختم شركة إيكوم"
                class="stamp-image"
              />
            </div>
          </div>

          <div class="signature-box">
            <div class="signature-title">توقيع المسؤول</div>
            <div class="signature-line"></div>
            <div class="signature-name">Muhammad Ibrahim</div>
          </div>
        </div>
      </div>

      <div class="footer">
        <p>
          شركة إيكوم للحلول التقنية - تصميم المواقع والمتاجر الإلكترونية
          والبرمجة
        </p>
        <p>للاستفسار: <EMAIL></p>
      </div>
    </div>

    <script>
      // Set current date
      const now = new Date();
      const options = {
        year: "numeric",
        month: "long",
        day: "numeric",
        weekday: "long",
      };
      document.getElementById("current-date").textContent =
        now.toLocaleDateString("ar-EG", options);
    </script>
  </body>
</html>
