<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>إيصال استلام - شركة إيكوم</title>
    <link
      href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "IBM Plex Sans Arabic", sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 20px;
        direction: rtl;
        color: black;
        min-height: 100vh;
      }

      .receipt-container {
        max-width: 900px;
        margin: 0 auto;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
      }

      .receipt-container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #000, #666, #000);
      }

      .header {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        color: black;
        padding: 40px 30px;
        text-align: center;
        border-bottom: 3px solid black;
        position: relative;
      }

      .header::after {
        content: "";
        position: absolute;
        bottom: -3px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 6px;
        background: black;
        border-radius: 3px;
      }

      .logo {
        max-width: 180px;
        height: auto;
        margin-bottom: 25px;
        background: white;
        padding: 15px;
        border: 3px solid black;
        border-radius: 50%;
        transition: transform 0.3s ease;
      }

      .logo:hover {
        transform: scale(1.05);
      }

      .company-name {
        font-size: clamp(24px, 4vw, 32px);
        font-weight: 700;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
      }

      .receipt-title {
        font-size: clamp(20px, 3vw, 26px);
        margin-top: 25px;
        border: 3px solid black;
        padding: 15px 25px;
        background: black;
        color: white;
        border-radius: 50px;
        display: inline-block;
        position: relative;
        overflow: hidden;
      }

      .receipt-title::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .receipt-title:hover::before {
        left: 100%;
      }

      .receipt-body {
        padding: clamp(20px, 5vw, 50px);
      }

      .receipt-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
      }

      .info-item {
        padding: 20px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 2px solid black;
        border-radius: 15px;
        position: relative;
        transition: all 0.3s ease;
      }

      .info-item::before {
        content: "";
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #000, #666, #000);
        border-radius: 15px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .info-item:hover::before {
        opacity: 1;
      }

      .info-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }

      .info-label {
        font-weight: 600;
        color: black;
        margin-bottom: 8px;
        font-size: clamp(14px, 2.5vw, 16px);
      }

      .info-value {
        font-size: clamp(16px, 3vw, 18px);
        color: black;
        font-weight: 500;
      }

      .service-details {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        padding: 35px;
        margin: 40px 0;
        border: 3px solid black;
        border-radius: 20px;
        position: relative;
        overflow: hidden;
      }

      .service-details::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
          45deg,
          transparent,
          transparent 10px,
          rgba(0, 0, 0, 0.02) 10px,
          rgba(0, 0, 0, 0.02) 20px
        );
        pointer-events: none;
      }

      .service-title {
        font-size: 20px;
        font-weight: bold;
        color: black;
        margin-bottom: 15px;
        text-align: center;
      }

      .service-description {
        font-size: 16px;
        line-height: 1.6;
        color: black;
        text-align: center;
      }

      .website-url {
        background: black;
        color: white;
        padding: 10px;
        text-align: center;
        margin-top: 15px;
        font-weight: bold;
      }

      .amount-section {
        background: linear-gradient(135deg, #000000 0%, #434343 100%);
        color: white;
        padding: 35px;
        text-align: center;
        margin: 40px 0;
        border-radius: 20px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
      }

      .amount-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        animation: shimmer 3s infinite;
      }

      @keyframes shimmer {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }

      .amount-label {
        font-size: clamp(16px, 3vw, 20px);
        margin-bottom: 15px;
        position: relative;
        z-index: 1;
      }

      .amount-value {
        font-size: clamp(32px, 6vw, 42px);
        font-weight: 700;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 1;
      }

      .amount-words {
        font-size: clamp(14px, 2.5vw, 18px);
        font-style: italic;
        opacity: 0.9;
        position: relative;
        z-index: 1;
      }

      .signature-section {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 40px;
        margin-top: 60px;
        padding-top: 40px;
        border-top: 3px solid black;
        position: relative;
      }

      .signature-section::before {
        content: "";
        position: absolute;
        top: -3px;
        left: 50%;
        transform: translateX(-50%);
        width: 150px;
        height: 6px;
        background: black;
        border-radius: 3px;
      }

      .signature-box {
        text-align: center;
        padding: 20px;
      }

      .signature-title {
        font-weight: bold;
        color: black;
        margin-bottom: 60px;
        font-size: 18px;
      }

      .signature-line {
        border-bottom: 2px solid black;
        margin-bottom: 10px;
        height: 80px;
        position: relative;
      }

      .stamp-area {
        width: 150px;
        height: 150px;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
      }

      .stamp-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }

      .signature-name {
        font-family: "Brush Script MT", "Lucida Handwriting", "Segoe Script",
          cursive;
        font-style: italic;
        font-size: 24px;
        color: #2c3e50;
        transform: rotate(-3deg);
        margin-top: 15px;
        font-weight: 300;
        letter-spacing: 2px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        position: relative;
      }

      .signature-name::after {
        content: "";
        position: absolute;
        bottom: -5px;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(
          to right,
          transparent,
          #2c3e50,
          transparent
        );
      }

      .footer {
        background: black;
        color: white;
        padding: 20px;
        text-align: center;
      }

      .date-section {
        text-align: left;
        margin-bottom: 20px;
        font-size: 16px;
        color: black;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        body {
          padding: 10px;
        }

        .receipt-container {
          border-radius: 15px;
        }

        .header {
          padding: 30px 20px;
        }

        .logo {
          max-width: 150px;
        }

        .receipt-info {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .signature-section {
          grid-template-columns: 1fr;
          gap: 30px;
        }
      }

      @media (max-width: 480px) {
        .receipt-body {
          padding: 20px;
        }

        .service-details {
          padding: 25px;
        }

        .amount-section {
          padding: 25px;
        }
      }

      @media print {
        body {
          background: white !important;
          padding: 0;
        }

        .receipt-container {
          box-shadow: none !important;
          border-radius: 0;
          max-width: 100%;
        }

        .receipt-container::before,
        .amount-section::before,
        .receipt-title::before {
          display: none;
        }
      }
    </style>
  </head>
  <body>
    <div class="receipt-container">
      <div class="header">
        <img
          src="https://ecom-eg.net/wp-content/uploads/2023/04/E-COM-LOGO-DESIGN-1.png"
          alt="E-COM Logo"
          class="logo"
        />
        <div class="company-name">شركة إيكوم للحلول التقنية</div>
        <div class="receipt-title">إيصال استلام</div>
      </div>

      <div class="receipt-body">
        <div class="date-section">التاريخ: <span id="current-date"></span></div>

        <div class="receipt-info">
          <div class="info-item">
            <div class="info-label">رقم الإيصال:</div>
            <div class="info-value" id="receipt-number">#REC-2025-001</div>
          </div>
          <div class="info-item">
            <div class="info-label">العميل:</div>
            <div class="info-value">Rhym Promotion</div>
          </div>
        </div>

        <div class="service-details">
          <div class="service-title">تفاصيل الخدمة</div>
          <div class="service-description">
            ثمن خدمة تصميم وبرمجة موقع إلكتروني متكامل
            <div class="website-url">https://rhympromotion.ecom-eg.net/</div>
            <div style="font-size: 14px; margin-top: 10px; opacity: 0.8;">
              (الموقع قيد التطوير والتحديث)
            </div>
          </div>
        </div>

        <div class="amount-section">
          <div class="amount-label">المبلغ المستلم</div>
          <div class="amount-value">٩٦٠٠ جنيه مصري</div>
          <div class="amount-words">تسعة آلاف وستمائة جنيه مصري لا غير</div>
        </div>

        <div class="signature-section">
          <div class="signature-box">
            <div class="signature-title">ختم الشركة</div>
            <div class="stamp-area">
              <img
                src="../signature.png"
                alt="ختم شركة إيكوم"
                class="stamp-image"
              />
            </div>
          </div>

          <div class="signature-box">
            <div class="signature-title">توقيع المسؤول</div>
            <div class="signature-line"></div>
            <div class="signature-name">Muhammad Ibrahim</div>
          </div>
        </div>
      </div>

      <div class="footer">
        <p>
          شركة إيكوم للحلول التقنية - تصميم المواقع والمتاجر الإلكترونية
          والبرمجة
        </p>
        <p>للاستفسار: <EMAIL></p>
      </div>
    </div>

    <script>
      // Set current date
      const now = new Date();
      const options = {
        year: "numeric",
        month: "long",
        day: "numeric",
        weekday: "long",
      };
      document.getElementById("current-date").textContent =
        now.toLocaleDateString("ar-EG", options);

      // Generate random receipt number
      function generateReceiptNumber() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 9000) + 1000; // 4-digit random number
        return `#REC-${year}-${randomNum}`;
      }

      document.getElementById("receipt-number").textContent =
        generateReceiptNumber();
    </script>
  </body>
</html>
