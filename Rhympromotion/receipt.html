<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال استلام - شركة إيكوم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            direction: rtl;
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .logo {
            max-width: 200px;
            height: auto;
            margin-bottom: 20px;
            background: white;
            padding: 10px;
            border-radius: 10px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .receipt-title {
            font-size: 24px;
            margin-top: 20px;
            border: 2px solid white;
            padding: 10px;
            border-radius: 5px;
        }
        
        .receipt-body {
            padding: 40px;
        }
        
        .receipt-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-right: 4px solid #3498db;
        }
        
        .info-label {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-size: 16px;
            color: #34495e;
        }
        
        .service-details {
            background: #e8f4fd;
            padding: 25px;
            border-radius: 10px;
            margin: 30px 0;
            border: 2px solid #3498db;
        }
        
        .service-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .service-description {
            font-size: 16px;
            line-height: 1.6;
            color: #34495e;
            text-align: center;
        }
        
        .website-url {
            background: #2c3e50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            margin-top: 15px;
            font-weight: bold;
        }
        
        .amount-section {
            background: #27ae60;
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin: 30px 0;
        }
        
        .amount-label {
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .amount-value {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .amount-words {
            font-size: 16px;
            font-style: italic;
        }
        
        .signature-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #ecf0f1;
        }
        
        .signature-box {
            text-align: center;
            padding: 20px;
        }
        
        .signature-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 60px;
            font-size: 18px;
        }
        
        .signature-line {
            border-bottom: 2px solid #2c3e50;
            margin-bottom: 10px;
            height: 80px;
            position: relative;
        }
        
        .stamp-area {
            border: 3px dashed #e74c3c;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e74c3c;
            font-weight: bold;
            font-size: 14px;
            text-align: center;
        }
        
        .footer {
            background: #34495e;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .date-section {
            text-align: left;
            margin-bottom: 20px;
            font-size: 16px;
            color: #7f8c8d;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .receipt-container {
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <div class="header">
            <img src="https://ecom-eg.net/wp-content/uploads/2023/04/E-COM-LOGO-DESIGN-1.png" alt="E-COM Logo" class="logo">
            <div class="company-name">شركة إيكوم للحلول التقنية</div>
            <div class="receipt-title">إيصال استلام</div>
        </div>
        
        <div class="receipt-body">
            <div class="date-section">
                التاريخ: <span id="current-date"></span>
            </div>
            
            <div class="receipt-info">
                <div class="info-item">
                    <div class="info-label">رقم الإيصال:</div>
                    <div class="info-value">#REC-2024-001</div>
                </div>
                <div class="info-item">
                    <div class="info-label">العميل:</div>
                    <div class="info-value">Rhym Promotion</div>
                </div>
            </div>
            
            <div class="service-details">
                <div class="service-title">تفاصيل الخدمة</div>
                <div class="service-description">
                    ثمن خدمة تصميم وبرمجة موقع إلكتروني متكامل
                    <div class="website-url">
                        https://rhympromotion.com/
                    </div>
                </div>
            </div>
            
            <div class="amount-section">
                <div class="amount-label">المبلغ المستلم</div>
                <div class="amount-value">٩٦٠٠ جنيه مصري</div>
                <div class="amount-words">تسعة آلاف وستمائة جنيه مصري لا غير</div>
            </div>
            
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-title">ختم الشركة</div>
                    <div class="stamp-area">
                        ختم شركة إيكوم
                    </div>
                </div>
                
                <div class="signature-box">
                    <div class="signature-title">توقيع المسؤول</div>
                    <div class="signature-line"></div>
                    <div style="font-weight: bold; color: #2c3e50;">محمد إبراهيم</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>شركة إيكوم للحلول التقنية - تصميم المواقع والمتاجر الإلكترونية والبرمجة</p>
            <p>للاستفسار: <EMAIL></p>
        </div>
    </div>
    
    <script>
        // Set current date
        const now = new Date();
        const options = { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
        };
        document.getElementById('current-date').textContent = now.toLocaleDateString('ar-EG', options);
    </script>
</body>
</html>
