<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rhym Promotion - خدمات التسويق والترويج</title>
    <meta name="description" content="شركة Rhym Promotion - خدمات التسويق الرقمي والترويج الإبداعي">
    <meta name="robots" content="noindex, nofollow">
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet" crossorigin="anonymous">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'IBM Plex Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            direction: rtl;
            color: black;
            min-height: 100vh;
        }

        .page-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }
        
        .page-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #000, #666, #000);
        }

        .header {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            color: black;
            padding: 40px 30px;
            text-align: center;
            border-bottom: 3px solid black;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 6px;
            background: black;
            border-radius: 3px;
        }

        .logo {
            max-width: 180px;
            height: auto;
            margin-bottom: 25px;
            background: white;
            padding: 15px;
            border: 3px solid black;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        
        .logo:hover {
            transform: scale(1.05);
        }

        .company-name {
            font-size: clamp(32px, 6vw, 48px);
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .tagline {
            font-size: clamp(18px, 3vw, 24px);
            margin-bottom: 25px;
            color: #333;
        }

        .status-badge {
            font-size: clamp(16px, 2.5vw, 20px);
            margin-top: 20px;
            border: 3px solid black;
            padding: 15px 25px;
            background: black;
            color: white;
            border-radius: 50px;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .main-content {
            padding: clamp(30px, 5vw, 50px);
        }

        .services-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: clamp(24px, 4vw, 32px);
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
            color: black;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .service-card {
            padding: 30px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid black;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .service-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .service-title {
            font-size: clamp(18px, 3vw, 22px);
            font-weight: 600;
            margin-bottom: 15px;
            color: black;
        }

        .service-desc {
            font-size: clamp(14px, 2.5vw, 16px);
            line-height: 1.6;
            color: #333;
        }

        .contact-section {
            background: linear-gradient(135deg, #000000 0%, #434343 100%);
            color: white;
            padding: 40px;
            text-align: center;
            margin: 40px 0;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .contact-title {
            font-size: clamp(24px, 4vw, 32px);
            font-weight: 700;
            margin-bottom: 20px;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .contact-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-label {
            font-weight: 600;
            margin-bottom: 8px;
            font-size: clamp(14px, 2.5vw, 16px);
        }

        .contact-value {
            font-size: clamp(14px, 2.5vw, 16px);
            opacity: 0.9;
        }

        .footer {
            background: black;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .footer p {
            margin-bottom: 10px;
        }

        .ecom-credit {
            opacity: 0.8;
            font-size: 14px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .page-container {
                border-radius: 15px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .logo {
                max-width: 150px;
            }
            
            .services-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .contact-grid {
                grid-template-columns: 1fr;
            }
        }

        @media print {
            body {
                background: white !important;
                padding: 0;
            }

            .page-container {
                box-shadow: none !important;
                border-radius: 0;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <div class="header">
            <img src="https://ecom-eg.net/wp-content/uploads/2023/04/E-COM-LOGO-DESIGN-1.png" alt="E-COM Logo" class="logo">
            <div class="company-name">Rhym Promotion</div>
            <div class="tagline">خدمات التسويق الرقمي والترويج الإبداعي</div>
            <div class="status-badge">🚀 الموقع قيد التطوير والتحديث</div>
        </div>

        <div class="main-content">
            <section class="services-section">
                <h2 class="section-title">خدماتنا</h2>
                
                <div class="services-grid">
                    <div class="service-card">
                        <span class="service-icon">📱</span>
                        <h3 class="service-title">التسويق الرقمي</h3>
                        <p class="service-desc">استراتيجيات تسويقية متطورة عبر منصات التواصل الاجتماعي والإنترنت لزيادة الوصول والمبيعات</p>
                    </div>

                    <div class="service-card">
                        <span class="service-icon">🎨</span>
                        <h3 class="service-title">التصميم الإبداعي</h3>
                        <p class="service-desc">تصميم هوية بصرية مميزة ومحتوى إبداعي يجذب العملاء ويعكس قيم علامتك التجارية</p>
                    </div>

                    <div class="service-card">
                        <span class="service-icon">📊</span>
                        <h3 class="service-title">إدارة الحملات</h3>
                        <p class="service-desc">إدارة وتحليل الحملات الإعلانية المدفوعة لضمان أفضل النتائج وأعلى عائد استثمار</p>
                    </div>
                </div>
            </section>

            <section class="contact-section">
                <h2 class="contact-title">تواصل معنا</h2>
                <p style="font-size: clamp(16px, 3vw, 18px); margin-bottom: 20px;">نحن هنا لمساعدتك في تحقيق أهدافك التسويقية</p>
                
                <div class="contact-grid">
                    <div class="contact-item">
                        <div class="contact-label">📧 البريد الإلكتروني</div>
                        <div class="contact-value"><EMAIL></div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-label">📱 الهاتف</div>
                        <div class="contact-value">+20 XXX XXX XXXX</div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-label">🌐 الموقع</div>
                        <div class="contact-value">rhympromotion.ecom-eg.net</div>
                    </div>
                </div>
            </section>
        </div>

        <footer class="footer">
            <p>&copy; 2025 Rhym Promotion. جميع الحقوق محفوظة.</p>
            <p class="ecom-credit">تم التطوير بواسطة <strong>شركة إيكوم للحلول التقنية</strong></p>
        </footer>
    </div>
</body>
</html>
