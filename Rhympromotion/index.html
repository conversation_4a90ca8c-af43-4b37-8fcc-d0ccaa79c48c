<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rhym Promotion - خدمات التسويق والترويج</title>
    <meta name="description" content="شركة Rhym Promotion - خدمات التسويق الرقمي والترويج الإبداعي">
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'IBM Plex Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            padding: 60px 0;
            position: relative;
        }

        .logo {
            font-size: clamp(48px, 8vw, 72px);
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .tagline {
            font-size: clamp(18px, 3vw, 24px);
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .services {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 60px 0;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .service-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .service-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .service-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .service-desc {
            font-size: 16px;
            line-height: 1.6;
            opacity: 0.9;
        }

        .cta-section {
            text-align: center;
            padding: 60px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 30px;
            margin: 40px 0;
            backdrop-filter: blur(10px);
        }

        .cta-title {
            font-size: clamp(28px, 5vw, 36px);
            font-weight: 700;
            margin-bottom: 20px;
        }

        .cta-text {
            font-size: 18px;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .contact-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }

        .footer {
            text-align: center;
            padding: 40px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 60px;
        }

        .status-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            margin-top: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header {
                padding: 40px 0;
            }
            
            .services {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .service-card {
                padding: 30px;
            }
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="container">
        <header class="header">
            <h1 class="logo">Rhym Promotion</h1>
            <p class="tagline">خدمات التسويق الرقمي والترويج الإبداعي</p>
            <div class="status-badge">🚀 الموقع قيد التطوير والتحديث</div>
        </header>

        <section class="services">
            <div class="service-card">
                <span class="service-icon">📱</span>
                <h3 class="service-title">التسويق الرقمي</h3>
                <p class="service-desc">استراتيجيات تسويقية متطورة عبر منصات التواصل الاجتماعي والإنترنت</p>
            </div>

            <div class="service-card">
                <span class="service-icon">🎨</span>
                <h3 class="service-title">التصميم الإبداعي</h3>
                <p class="service-desc">تصميم هوية بصرية مميزة ومحتوى إبداعي يجذب العملاء</p>
            </div>

            <div class="service-card">
                <span class="service-icon">📊</span>
                <h3 class="service-title">إدارة الحملات</h3>
                <p class="service-desc">إدارة وتحليل الحملات الإعلانية لضمان أفضل النتائج</p>
            </div>
        </section>

        <section class="cta-section">
            <h2 class="cta-title">ابدأ رحلتك معنا</h2>
            <p class="cta-text">نحن هنا لمساعدتك في تحقيق أهدافك التسويقية</p>
            
            <div class="contact-info">
                <div class="contact-item">
                    <h4>📧 البريد الإلكتروني</h4>
                    <p><EMAIL></p>
                </div>
                <div class="contact-item">
                    <h4>📱 الهاتف</h4>
                    <p>+20 XXX XXX XXXX</p>
                </div>
                <div class="contact-item">
                    <h4>🌐 الموقع</h4>
                    <p>rhympromotion.ecom-eg.net</p>
                </div>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Rhym Promotion. جميع الحقوق محفوظة.</p>
            <p style="margin-top: 10px; opacity: 0.7;">تم التطوير بواسطة <strong>شركة إيكوم</strong></p>
        </div>
    </footer>
</body>
</html>
